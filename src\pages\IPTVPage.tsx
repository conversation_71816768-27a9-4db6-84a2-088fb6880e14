
import { useState } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Navbar } from '@/components/navbar';
import { Footer } from '@/components/footer';
import { Search, Copy, Download, Calendar } from 'lucide-react';
import { toast } from 'sonner';

export default function IPTVPage() {
  const [searchTerm, setSearchTerm] = useState('');

  const iptvData = {
    xtream: [
      {
        id: 1,
        title: "Premium Sports Package",
        description: "Complete sports coverage with HD channels",
        host: "iptv.example.com",
        port: "8080",
        username: "sports_user",
        password: "sports_pass",
        expiry: "2024-07-15",
        tags: ["Sports", "HD", "Live"]
      },
      {
        id: 2,
        title: "Global Entertainment",
        description: "International channels from around the world",
        host: "global.iptv.com",
        port: "8080",
        username: "global_user",
        password: "global_pass",
        expiry: "2024-08-20",
        tags: ["International", "Movies", "TV Shows"]
      }
    ],
    mac: [
      {
        id: 1,
        title: "Arabic IPTV Collection",
        description: "Arabic channels with premium content",
        portal: "http://portal.example.com/stalker_portal/c/",
        mac: "00:1A:79:XX:XX:XX",
        expiry: "2024-06-30",
        tags: ["Arabic", "Premium", "Entertainment"]
      }
    ],
    m3u: [
      {
        id: 1,
        title: "USA Channels Playlist",
        description: "US channels in high quality",
        url: "http://example.com/playlist.m3u",
        expiry: "2024-09-10",
        tags: ["USA", "HD", "Entertainment"]
      }
    ],
    files: [
      {
        id: 1,
        title: "European Sports Bundle",
        description: "European sports channels collection",
        filename: "european_sports.m3u",
        size: "2.5 MB",
        expiry: "2024-07-25",
        tags: ["Europe", "Sports", "Bundle"]
      }
    ]
  };

  const copyToClipboard = (text: string, type: string) => {
    navigator.clipboard.writeText(text);
    toast.success(`${type} copied to clipboard!`);
  };

  const formatExpiry = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = date.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays < 0) return "Expired";
    if (diffDays === 0) return "Expires today";
    if (diffDays === 1) return "Expires tomorrow";
    return `${diffDays} days left`;
  };

  const XtreamCard = ({ item }) => (
    <Card className="hover:shadow-lg transition-shadow">
      <CardHeader>
        <div className="flex items-center justify-between">
          <Badge variant="outline" className="text-purple-500 border-purple-500/50">
            Xtream Codes
          </Badge>
          <Badge variant={item.expiry && new Date(item.expiry) > new Date() ? "secondary" : "destructive"}>
            {item.expiry ? formatExpiry(item.expiry) : "No expiry"}
          </Badge>
        </div>
        <CardTitle className="text-lg">{item.title}</CardTitle>
        <CardDescription>{item.description}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <div className="grid grid-cols-2 gap-2 text-sm">
            <div>
              <span className="font-medium">Host:</span>
              <div className="flex items-center gap-2">
                <span className="text-muted-foreground">{item.host}</span>
                <Button size="sm" variant="ghost" onClick={() => copyToClipboard(item.host, "Host")}>
                  <Copy className="w-3 h-3" />
                </Button>
              </div>
            </div>
            <div>
              <span className="font-medium">Port:</span>
              <div className="flex items-center gap-2">
                <span className="text-muted-foreground">{item.port}</span>
                <Button size="sm" variant="ghost" onClick={() => copyToClipboard(item.port, "Port")}>
                  <Copy className="w-3 h-3" />
                </Button>
              </div>
            </div>
            <div>
              <span className="font-medium">Username:</span>
              <div className="flex items-center gap-2">
                <span className="text-muted-foreground">{item.username}</span>
                <Button size="sm" variant="ghost" onClick={() => copyToClipboard(item.username, "Username")}>
                  <Copy className="w-3 h-3" />
                </Button>
              </div>
            </div>
            <div>
              <span className="font-medium">Password:</span>
              <div className="flex items-center gap-2">
                <span className="text-muted-foreground">{item.password}</span>
                <Button size="sm" variant="ghost" onClick={() => copyToClipboard(item.password, "Password")}>
                  <Copy className="w-3 h-3" />
                </Button>
              </div>
            </div>
          </div>
          <div className="flex flex-wrap gap-2">
            {item.tags.map((tag, index) => (
              <Badge key={index} variant="outline" className="text-xs">
                {tag}
              </Badge>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );

  const MacCard = ({ item }) => (
    <Card className="hover:shadow-lg transition-shadow">
      <CardHeader>
        <div className="flex items-center justify-between">
          <Badge variant="outline" className="text-blue-500 border-blue-500/50">
            MAC Portal
          </Badge>
          <Badge variant={item.expiry && new Date(item.expiry) > new Date() ? "secondary" : "destructive"}>
            {item.expiry ? formatExpiry(item.expiry) : "No expiry"}
          </Badge>
        </div>
        <CardTitle className="text-lg">{item.title}</CardTitle>
        <CardDescription>{item.description}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <div className="space-y-2 text-sm">
            <div>
              <span className="font-medium">Portal URL:</span>
              <div className="flex items-center gap-2">
                <span className="text-muted-foreground break-all">{item.portal}</span>
                <Button size="sm" variant="ghost" onClick={() => copyToClipboard(item.portal, "Portal URL")}>
                  <Copy className="w-3 h-3" />
                </Button>
              </div>
            </div>
            <div>
              <span className="font-medium">MAC Address:</span>
              <div className="flex items-center gap-2">
                <span className="text-muted-foreground">{item.mac}</span>
                <Button size="sm" variant="ghost" onClick={() => copyToClipboard(item.mac, "MAC Address")}>
                  <Copy className="w-3 h-3" />
                </Button>
              </div>
            </div>
          </div>
          <div className="flex flex-wrap gap-2">
            {item.tags.map((tag, index) => (
              <Badge key={index} variant="outline" className="text-xs">
                {tag}
              </Badge>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );

  const M3uCard = ({ item }) => (
    <Card className="hover:shadow-lg transition-shadow">
      <CardHeader>
        <div className="flex items-center justify-between">
          <Badge variant="outline" className="text-green-500 border-green-500/50">
            M3U Playlist
          </Badge>
          <Badge variant={item.expiry && new Date(item.expiry) > new Date() ? "secondary" : "destructive"}>
            {item.expiry ? formatExpiry(item.expiry) : "No expiry"}
          </Badge>
        </div>
        <CardTitle className="text-lg">{item.title}</CardTitle>
        <CardDescription>{item.description}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <div className="text-sm">
            <span className="font-medium">Playlist URL:</span>
            <div className="flex items-center gap-2">
              <span className="text-muted-foreground break-all">{item.url}</span>
              <Button size="sm" variant="ghost" onClick={() => copyToClipboard(item.url, "Playlist URL")}>
                <Copy className="w-3 h-3" />
              </Button>
            </div>
          </div>
          <div className="flex flex-wrap gap-2">
            {item.tags.map((tag, index) => (
              <Badge key={index} variant="outline" className="text-xs">
                {tag}
              </Badge>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );

  const FileCard = ({ item }) => (
    <Card className="hover:shadow-lg transition-shadow">
      <CardHeader>
        <div className="flex items-center justify-between">
          <Badge variant="outline" className="text-orange-500 border-orange-500/50">
            File Download
          </Badge>
          <Badge variant={item.expiry && new Date(item.expiry) > new Date() ? "secondary" : "destructive"}>
            {item.expiry ? formatExpiry(item.expiry) : "No expiry"}
          </Badge>
        </div>
        <CardTitle className="text-lg">{item.title}</CardTitle>
        <CardDescription>{item.description}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <div className="grid grid-cols-2 gap-2 text-sm">
            <div>
              <span className="font-medium">Filename:</span>
              <span className="text-muted-foreground ml-2">{item.filename}</span>
            </div>
            <div>
              <span className="font-medium">Size:</span>
              <span className="text-muted-foreground ml-2">{item.size}</span>
            </div>
          </div>
          <div className="flex flex-wrap gap-2 mb-3">
            {item.tags.map((tag, index) => (
              <Badge key={index} variant="outline" className="text-xs">
                {tag}
              </Badge>
            ))}
          </div>
          <Button className="w-full" variant="outline">
            <Download className="w-4 h-4 mr-2" />
            Download File
          </Button>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="min-h-screen bg-background">
      <Navbar />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold mb-4">
            Browse <span className="bg-gradient-to-r from-purple-500 to-blue-600 bg-clip-text text-transparent">IPTV Content</span>
          </h1>
          <p className="text-muted-foreground mb-6">
            Discover premium IPTV content across multiple formats and platforms
          </p>
          
          <div className="relative max-w-md mx-auto">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
            <Input
              placeholder="Search IPTV content..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        <Tabs defaultValue="xtream" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="xtream">Xtream Codes</TabsTrigger>
            <TabsTrigger value="mac">MAC Portals</TabsTrigger>
            <TabsTrigger value="m3u">M3U Playlists</TabsTrigger>
            <TabsTrigger value="files">File Downloads</TabsTrigger>
          </TabsList>
          
          <TabsContent value="xtream" className="mt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {iptvData.xtream.map((item) => (
                <XtreamCard key={item.id} item={item} />
              ))}
            </div>
          </TabsContent>
          
          <TabsContent value="mac" className="mt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {iptvData.mac.map((item) => (
                <MacCard key={item.id} item={item} />
              ))}
            </div>
          </TabsContent>
          
          <TabsContent value="m3u" className="mt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {iptvData.m3u.map((item) => (
                <M3uCard key={item.id} item={item} />
              ))}
            </div>
          </TabsContent>
          
          <TabsContent value="files" className="mt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {iptvData.files.map((item) => (
                <FileCard key={item.id} item={item} />
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </div>

      <Footer />
    </div>
  );
}
