
import { Link } from 'react-router-dom';
import { Tv } from 'lucide-react';

export function Footer() {
  return (
    <footer className="bg-background border-t border-border">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div className="col-span-1 md:col-span-2">
            <Link to="/" className="flex items-center space-x-2 mb-4">
              <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg flex items-center justify-center">
                <Tv className="w-5 h-5 text-white" />
              </div>
              <span className="text-xl font-bold bg-gradient-to-r from-purple-500 to-blue-600 bg-clip-text text-transparent">
                IPTV Hub
              </span>
            </Link>
            <p className="text-muted-foreground mb-4">
              Your premier destination for IPTV content, apps, and resources. 
              Discover the latest streaming solutions and stay updated with our blog.
            </p>
            <p className="text-sm text-muted-foreground">
              © 2024 IPTV Hub. All rights reserved.
            </p>
          </div>
          
          <div>
            <h3 className="font-semibold mb-4">Quick Links</h3>
            <ul className="space-y-2">
              <li><Link to="/iptv" className="text-muted-foreground hover:text-purple-500 transition-colors">Browse IPTV</Link></li>
              <li><Link to="/apps" className="text-muted-foreground hover:text-purple-500 transition-colors">Apps & Players</Link></li>
              <li><Link to="/blog" className="text-muted-foreground hover:text-purple-500 transition-colors">Blog</Link></li>
              <li><Link to="/contact" className="text-muted-foreground hover:text-purple-500 transition-colors">Contact</Link></li>
            </ul>
          </div>
          
          <div>
            <h3 className="font-semibold mb-4">Legal</h3>
            <ul className="space-y-2">
              <li><Link to="/about" className="text-muted-foreground hover:text-purple-500 transition-colors">About Us</Link></li>
              <li><Link to="/privacy" className="text-muted-foreground hover:text-purple-500 transition-colors">Privacy Policy</Link></li>
              <li><Link to="/terms" className="text-muted-foreground hover:text-purple-500 transition-colors">Terms of Use</Link></li>
            </ul>
          </div>
        </div>
        
        <div className="border-t border-border mt-8 pt-8 text-center">
          <p className="text-sm text-muted-foreground">
            This website is for educational purposes only. We do not host or distribute any copyrighted content.
          </p>
        </div>
      </div>
    </footer>
  );
}
