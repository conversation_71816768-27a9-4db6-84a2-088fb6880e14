// IPTV Auto-Detection Service
import { IPTVEntry } from './api';

export interface DetectionResult {
  type: 'xtream' | 'mac' | 'm3u' | 'unknown';
  confidence: number;
  extractedData: Partial<IPTVEntry>;
  errors?: string[];
}

export interface BulkDetectionResult {
  detected: DetectionResult[];
  summary: {
    total: number;
    xtream: number;
    mac: number;
    m3u: number;
    unknown: number;
  };
}

class IPTVDetector {
  // Xtream Codes patterns
  private xtreamPatterns = {
    host: /(?:https?:\/\/)?([a-zA-Z0-9.-]+)/,
    port: /:(\d{2,5})/,
    username: /username[:\s=]+([^\s,;]+)/i,
    password: /password[:\s=]+([^\s,;]+)/i,
    fullUrl: /https?:\/\/([^:\/\s]+):(\d+)\/.*username=([^&\s]+).*password=([^&\s]+)/i,
  };

  // MAC Portal patterns
  private macPatterns = {
    portal: /(https?:\/\/[^\/\s]+\/stalker_portal\/[^\s]*)/i,
    mac: /([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})/,
    macSimple: /mac[:\s=]+([0-9A-Fa-f:]{17})/i,
  };

  // M3U patterns
  private m3uPatterns = {
    url: /(https?:\/\/[^\s]+\.m3u[8]?)/i,
    header: /#EXTM3U/i,
    entry: /#EXTINF:.*,(.+)/i,
  };

  detectType(input: string): DetectionResult {
    const cleanInput = input.trim();
    
    // Try to detect each type
    const xtreamResult = this.detectXtream(cleanInput);
    const macResult = this.detectMAC(cleanInput);
    const m3uResult = this.detectM3U(cleanInput);

    // Return the result with highest confidence
    const results = [xtreamResult, macResult, m3uResult]
      .filter(r => r.confidence > 0)
      .sort((a, b) => b.confidence - a.confidence);

    return results[0] || {
      type: 'unknown',
      confidence: 0,
      extractedData: {},
      errors: ['Unable to detect IPTV type from input'],
    };
  }

  private detectXtream(input: string): DetectionResult {
    const result: DetectionResult = {
      type: 'xtream',
      confidence: 0,
      extractedData: {},
      errors: [],
    };

    // Check for full Xtream URL
    const fullUrlMatch = input.match(this.xtreamPatterns.fullUrl);
    if (fullUrlMatch) {
      result.confidence = 0.95;
      result.extractedData = {
        type: 'xtream',
        host: fullUrlMatch[1],
        port: fullUrlMatch[2],
        username: fullUrlMatch[3],
        password: fullUrlMatch[4],
      };
      return result;
    }

    // Check for individual components
    let confidence = 0;
    const extractedData: any = { type: 'xtream' };

    const hostMatch = input.match(this.xtreamPatterns.host);
    if (hostMatch) {
      extractedData.host = hostMatch[1];
      confidence += 0.3;
    }

    const portMatch = input.match(this.xtreamPatterns.port);
    if (portMatch) {
      extractedData.port = portMatch[1];
      confidence += 0.2;
    }

    const usernameMatch = input.match(this.xtreamPatterns.username);
    if (usernameMatch) {
      extractedData.username = usernameMatch[1];
      confidence += 0.25;
    }

    const passwordMatch = input.match(this.xtreamPatterns.password);
    if (passwordMatch) {
      extractedData.password = passwordMatch[1];
      confidence += 0.25;
    }

    // Check for Xtream-specific keywords
    if (/xtream|get\.php|player_api\.php/i.test(input)) {
      confidence += 0.2;
    }

    result.confidence = confidence;
    result.extractedData = extractedData;

    if (confidence < 0.3) {
      result.errors.push('Insufficient Xtream Codes data detected');
    }

    return result;
  }

  private detectMAC(input: string): DetectionResult {
    const result: DetectionResult = {
      type: 'mac',
      confidence: 0,
      extractedData: {},
      errors: [],
    };

    let confidence = 0;
    const extractedData: any = { type: 'mac' };

    // Check for portal URL
    const portalMatch = input.match(this.macPatterns.portal);
    if (portalMatch) {
      extractedData.portal = portalMatch[1];
      confidence += 0.6;
    }

    // Check for MAC address
    const macMatch = input.match(this.macPatterns.mac) || input.match(this.macPatterns.macSimple);
    if (macMatch) {
      extractedData.mac = macMatch[0];
      confidence += 0.4;
    }

    // Check for MAC-specific keywords
    if (/stalker|portal|mag\d+/i.test(input)) {
      confidence += 0.2;
    }

    result.confidence = confidence;
    result.extractedData = extractedData;

    if (confidence < 0.4) {
      result.errors.push('Insufficient MAC Portal data detected');
    }

    return result;
  }

  private detectM3U(input: string): DetectionResult {
    const result: DetectionResult = {
      type: 'm3u',
      confidence: 0,
      extractedData: {},
      errors: [],
    };

    let confidence = 0;
    const extractedData: any = { type: 'm3u' };

    // Check for M3U URL
    const urlMatch = input.match(this.m3uPatterns.url);
    if (urlMatch) {
      extractedData.url = urlMatch[1];
      confidence += 0.8;
    }

    // Check for M3U header
    if (this.m3uPatterns.header.test(input)) {
      confidence += 0.7;
    }

    // Check for M3U entries
    const entryMatches = input.match(this.m3uPatterns.entry);
    if (entryMatches) {
      confidence += 0.3;
    }

    // Check for M3U-specific keywords
    if (/\.m3u8?|#EXTINF|#EXT-X/i.test(input)) {
      confidence += 0.2;
    }

    result.confidence = confidence;
    result.extractedData = extractedData;

    if (confidence < 0.3) {
      result.errors.push('Insufficient M3U data detected');
    }

    return result;
  }

  bulkDetect(inputs: string[]): BulkDetectionResult {
    const detected = inputs.map(input => this.detectType(input));
    
    const summary = {
      total: detected.length,
      xtream: detected.filter(d => d.type === 'xtream').length,
      mac: detected.filter(d => d.type === 'mac').length,
      m3u: detected.filter(d => d.type === 'm3u').length,
      unknown: detected.filter(d => d.type === 'unknown').length,
    };

    return { detected, summary };
  }

  parseTextInput(text: string): string[] {
    // Split by common delimiters and clean up
    return text
      .split(/\n|;|,|\|/)
      .map(line => line.trim())
      .filter(line => line.length > 10); // Filter out very short lines
  }

  generateIPTVEntry(detection: DetectionResult, name?: string): Partial<IPTVEntry> {
    const baseEntry: Partial<IPTVEntry> = {
      name: name || `Auto-detected ${detection.type.toUpperCase()}`,
      type: detection.type as any,
      description: `Auto-detected ${detection.type} entry`,
      status: 'active',
      tags: ['auto-detected', detection.type],
      ...detection.extractedData,
    };

    // Add type-specific defaults
    switch (detection.type) {
      case 'xtream':
        if (!baseEntry.port && baseEntry.host) {
          baseEntry.port = '8080'; // Default Xtream port
        }
        break;
      case 'mac':
        // MAC entries are usually ready as-is
        break;
      case 'm3u':
        // M3U entries are usually ready as-is
        break;
    }

    return baseEntry;
  }

  validateDetection(detection: DetectionResult): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (detection.confidence < 0.5) {
      errors.push('Low confidence detection');
    }

    switch (detection.type) {
      case 'xtream':
        if (!detection.extractedData.host) errors.push('Missing host');
        if (!detection.extractedData.username) errors.push('Missing username');
        if (!detection.extractedData.password) errors.push('Missing password');
        break;
      case 'mac':
        if (!detection.extractedData.portal) errors.push('Missing portal URL');
        if (!detection.extractedData.mac) errors.push('Missing MAC address');
        break;
      case 'm3u':
        if (!detection.extractedData.url) errors.push('Missing M3U URL');
        break;
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }
}

export const iptvDetector = new IPTVDetector();
