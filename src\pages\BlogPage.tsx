
import { Link } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Navbar } from '@/components/navbar';
import { Footer } from '@/components/footer';
import { Calendar, User, ArrowRight } from 'lucide-react';

export default function BlogPage() {
  const blogPosts = [
    {
      id: 1,
      title: "Complete Guide to IPTV Setup in 2024",
      slug: "complete-guide-iptv-setup-2024",
      excerpt: "Learn how to set up IPTV on any device with our comprehensive guide covering all major platforms and players.",
      category: "Tutorial",
      author: "IPTV Hub Team",
      publishDate: "2024-01-15",
      readTime: "8 min read",
      tags: ["IPTV", "Setup", "Tutorial", "Guide"]
    },
    {
      id: 2,
      title: "Best IPTV Players Comparison 2024",
      slug: "best-iptv-players-comparison-2024",
      excerpt: "Detailed comparison of the top IPTV players including features, pricing, and performance analysis.",
      category: "Review",
      author: "Tech Reviewer",
      publishDate: "2024-01-12",
      readTime: "12 min read",
      tags: ["IPTV", "Players", "Review", "Comparison"]
    },
    {
      id: 3,
      title: "Understanding IPTV Protocols: HLS vs RTMP vs UDP",
      slug: "understanding-iptv-protocols-hls-rtmp-udp",
      excerpt: "Technical deep dive into different IPTV streaming protocols and when to use each one.",
      category: "Technical",
      author: "Network Engineer",
      publishDate: "2024-01-10",
      readTime: "15 min read",
      tags: ["IPTV", "Protocols", "Technical", "Streaming"]
    },
    {
      id: 4,
      title: "Troubleshooting Common IPTV Issues",
      slug: "troubleshooting-common-iptv-issues",
      excerpt: "Solutions to the most common IPTV problems including buffering, connection issues, and playback errors.",
      category: "Support",
      author: "Support Team",
      publishDate: "2024-01-08",
      readTime: "10 min read",
      tags: ["IPTV", "Troubleshooting", "Support", "Fix"]
    },
    {
      id: 5,
      title: "Legal Aspects of IPTV: What You Need to Know",
      slug: "legal-aspects-iptv-what-you-need-know",
      excerpt: "Understanding the legal landscape of IPTV services and how to stay compliant with regulations.",
      category: "Legal",
      author: "Legal Expert",
      publishDate: "2024-01-05",
      readTime: "6 min read",
      tags: ["IPTV", "Legal", "Compliance", "Regulations"]
    },
    {
      id: 6,
      title: "Optimizing Your Network for IPTV Streaming",
      slug: "optimizing-network-iptv-streaming",
      excerpt: "Network optimization tips and techniques to ensure smooth IPTV streaming without buffering.",
      category: "Technical",
      author: "Network Specialist",
      publishDate: "2024-01-03",
      readTime: "9 min read",
      tags: ["IPTV", "Network", "Optimization", "Performance"]
    }
  ];

  const categories = ["All", "Tutorial", "Review", "Technical", "Support", "Legal"];

  return (
    <div className="min-h-screen bg-background">
      <Navbar />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold mb-4">
            IPTV <span className="bg-gradient-to-r from-purple-500 to-blue-600 bg-clip-text text-transparent">Blog</span>
          </h1>
          <p className="text-muted-foreground mb-6">
            Stay updated with the latest IPTV news, tutorials, and guides
          </p>
        </div>

        {/* Category Filter */}
        <div className="flex flex-wrap gap-2 mb-8 justify-center">
          {categories.map((category) => (
            <Badge
              key={category}
              variant={category === "All" ? "default" : "outline"}
              className="cursor-pointer hover:bg-primary hover:text-primary-foreground transition-colors"
            >
              {category}
            </Badge>
          ))}
        </div>

        {/* Featured Post */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold mb-6">Featured Article</h2>
          <Card className="hover:shadow-lg transition-shadow">
            <div className="md:flex">
              <div className="md:w-1/3 bg-gradient-to-br from-purple-500 to-blue-600 p-8 flex items-center justify-center">
                <div className="text-white text-center">
                  <h3 className="text-2xl font-bold mb-2">Featured</h3>
                  <p className="text-purple-100">Most Popular Article</p>
                </div>
              </div>
              <div className="md:w-2/3">
                <CardHeader>
                  <div className="flex items-center space-x-2 mb-2">
                    <Badge variant="outline" className="text-purple-500 border-purple-500/50">
                      {blogPosts[0].category}
                    </Badge>
                    <div className="flex items-center text-sm text-muted-foreground">
                      <Calendar className="w-4 h-4 mr-1" />
                      {new Date(blogPosts[0].publishDate).toLocaleDateString()}
                    </div>
                  </div>
                  <CardTitle className="text-xl mb-2">{blogPosts[0].title}</CardTitle>
                  <CardDescription>{blogPosts[0].excerpt}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                      <div className="flex items-center">
                        <User className="w-4 h-4 mr-1" />
                        {blogPosts[0].author}
                      </div>
                      <span>{blogPosts[0].readTime}</span>
                    </div>
                    <Link
                      to={`/blog/${blogPosts[0].slug}`}
                      className="flex items-center text-purple-500 hover:text-purple-600 transition-colors"
                    >
                      Read More <ArrowRight className="w-4 h-4 ml-1" />
                    </Link>
                  </div>
                </CardContent>
              </div>
            </div>
          </Card>
        </div>

        {/* Blog Posts Grid */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold mb-6">Latest Articles</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {blogPosts.slice(1).map((post) => (
              <Card key={post.id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-center space-x-2 mb-2">
                    <Badge variant="outline" className="text-xs">
                      {post.category}
                    </Badge>
                    <div className="flex items-center text-xs text-muted-foreground">
                      <Calendar className="w-3 h-3 mr-1" />
                      {new Date(post.publishDate).toLocaleDateString()}
                    </div>
                  </div>
                  <CardTitle className="text-lg mb-2">{post.title}</CardTitle>
                  <CardDescription className="text-sm">{post.excerpt}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex flex-wrap gap-1">
                      {post.tags.map((tag, index) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                        <User className="w-3 h-3" />
                        <span>{post.author}</span>
                        <span>•</span>
                        <span>{post.readTime}</span>
                      </div>
                      <Link
                        to={`/blog/${post.slug}`}
                        className="flex items-center text-purple-500 hover:text-purple-600 transition-colors text-sm"
                      >
                        Read <ArrowRight className="w-3 h-3 ml-1" />
                      </Link>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Newsletter Signup */}
        <div className="bg-gradient-to-r from-purple-500 to-blue-600 rounded-lg p-8 text-white text-center">
          <h3 className="text-2xl font-bold mb-4">Stay Updated</h3>
          <p className="mb-6">Subscribe to our newsletter for the latest IPTV news and tutorials</p>
          <div className="flex max-w-md mx-auto">
            <input
              type="email"
              placeholder="Enter your email"
              className="flex-1 px-4 py-2 rounded-l-lg text-gray-900"
            />
            <button className="px-6 py-2 bg-white text-purple-600 rounded-r-lg font-medium hover:bg-gray-100 transition-colors">
              Subscribe
            </button>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
}
