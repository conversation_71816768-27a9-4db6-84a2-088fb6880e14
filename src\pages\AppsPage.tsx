
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Navbar } from '@/components/navbar';
import { Footer } from '@/components/footer';
import { Download, Star, Shield, Smartphone, Monitor, Tv } from 'lucide-react';

export default function AppsPage() {
  const apps = [
    {
      id: 1,
      name: "IPTV Smarters Pro",
      category: "Android",
      description: "Professional IPTV player with advanced features and user-friendly interface",
      rating: 4.8,
      downloads: "10M+",
      features: ["EPG Support", "Parental Control", "Multi-language", "Catch-up TV"],
      platforms: ["Android", "iOS", "Windows", "macOS"],
      icon: Smartphone,
      playStoreUrl: "#",
      apkUrl: "#"
    },
    {
      id: 2,
      name: "TiviMate IPTV Player",
      category: "Android TV",
      description: "Premium IPTV player designed specifically for Android TV and Fire TV",
      rating: 4.9,
      downloads: "5M+",
      features: ["TV Guide", "Recording", "Multiple Playlists", "Picture-in-Picture"],
      platforms: ["Android TV", "Fire TV"],
      icon: Tv,
      playStoreUrl: "#",
      apkUrl: "#"
    },
    {
      id: 3,
      name: "VLC Media Player",
      category: "Cross-platform",
      description: "Free and open-source media player that supports IPTV streams",
      rating: 4.7,
      downloads: "1B+",
      features: ["Free", "Open Source", "Cross-platform", "Network Streaming"],
      platforms: ["Windows", "macOS", "Linux", "Android", "iOS"],
      icon: Monitor,
      playStoreUrl: "#",
      downloadUrl: "#"
    },
    {
      id: 4,
      name: "Perfect Player IPTV",
      category: "Android",
      description: "Lightweight IPTV player with customizable interface",
      rating: 4.6,
      downloads: "1M+",
      features: ["Lightweight", "Customizable", "EPG Support", "External Player"],
      platforms: ["Android"],
      icon: Smartphone,
      playStoreUrl: "#",
      apkUrl: "#"
    },
    {
      id: 5,
      name: "GSE Smart IPTV",
      category: "iOS",
      description: "Complete IPTV solution for iOS devices with advanced features",
      rating: 4.5,
      downloads: "500K+",
      features: ["iOS Optimized", "AirPlay", "Chromecast", "Cloud Sync"],
      platforms: ["iOS", "Apple TV"],
      icon: Smartphone,
      appStoreUrl: "#"
    },
    {
      id: 6,
      name: "Kodi Media Center",
      category: "Cross-platform",
      description: "Open-source media center with IPTV addon support",
      rating: 4.4,
      downloads: "50M+",
      features: ["Open Source", "Addon Support", "Media Library", "Remote Control"],
      platforms: ["Windows", "macOS", "Linux", "Android", "iOS"],
      icon: Monitor,
      downloadUrl: "#"
    }
  ];

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case 'Android':
      case 'iOS':
        return Smartphone;
      case 'Android TV':
      case 'Fire TV':
      case 'Apple TV':
        return Tv;
      default:
        return Monitor;
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Navbar />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold mb-4">
            IPTV <span className="bg-gradient-to-r from-purple-500 to-blue-600 bg-clip-text text-transparent">Apps & Players</span>
          </h1>
          <p className="text-muted-foreground mb-6">
            Discover the best IPTV players and applications for all platforms
          </p>
        </div>

        {/* Featured Apps */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold mb-6">Recommended Players</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {apps.map((app) => (
              <Card key={app.id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg flex items-center justify-center">
                        <app.icon className="w-6 h-6 text-white" />
                      </div>
                      <div>
                        <CardTitle className="text-lg">{app.name}</CardTitle>
                        <Badge variant="outline" className="text-xs">
                          {app.category}
                        </Badge>
                      </div>
                    </div>
                  </div>
                  <CardDescription>{app.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between text-sm">
                      <div className="flex items-center space-x-2">
                        <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                        <span>{app.rating}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Download className="w-4 h-4 text-muted-foreground" />
                        <span>{app.downloads}</span>
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <h4 className="font-medium text-sm">Features:</h4>
                      <div className="flex flex-wrap gap-1">
                        {app.features.map((feature, index) => (
                          <Badge key={index} variant="secondary" className="text-xs">
                            {feature}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <h4 className="font-medium text-sm">Platforms:</h4>
                      <div className="flex flex-wrap gap-1">
                        {app.platforms.map((platform, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {platform}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    
                    <div className="flex gap-2">
                      {app.playStoreUrl && (
                        <Button size="sm" variant="outline" className="flex-1">
                          <Download className="w-4 h-4 mr-2" />
                          Play Store
                        </Button>
                      )}
                      {app.appStoreUrl && (
                        <Button size="sm" variant="outline" className="flex-1">
                          <Download className="w-4 h-4 mr-2" />
                          App Store
                        </Button>
                      )}
                      {app.apkUrl && (
                        <Button size="sm" variant="outline" className="flex-1">
                          <Download className="w-4 h-4 mr-2" />
                          APK
                        </Button>
                      )}
                      {app.downloadUrl && (
                        <Button size="sm" variant="outline" className="flex-1">
                          <Download className="w-4 h-4 mr-2" />
                          Download
                        </Button>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Setup Instructions */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold mb-6">Setup Instructions</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Shield className="w-5 h-5 text-green-500" />
                  <span>Android Setup</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ol className="space-y-2 text-sm">
                  <li>1. Download the APK file or install from Play Store</li>
                  <li>2. Enable "Unknown Sources" in Settings if installing APK</li>
                  <li>3. Open the app and add your IPTV playlist</li>
                  <li>4. Configure EPG settings if available</li>
                  <li>5. Enjoy your IPTV content!</li>
                </ol>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Shield className="w-5 h-5 text-blue-500" />
                  <span>iOS Setup</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ol className="space-y-2 text-sm">
                  <li>1. Download the app from App Store</li>
                  <li>2. Open the app and go to Settings</li>
                  <li>3. Add your IPTV playlist URL or file</li>
                  <li>4. Configure player settings as needed</li>
                  <li>5. Start streaming your content!</li>
                </ol>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Tips Section */}
        <div className="bg-muted/50 rounded-lg p-6">
          <h3 className="text-xl font-bold mb-4">Pro Tips</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <h4 className="font-medium mb-2">For Best Performance:</h4>
              <ul className="space-y-1 text-muted-foreground">
                <li>• Use a stable internet connection</li>
                <li>• Enable hardware acceleration when available</li>
                <li>• Keep your apps updated</li>
                <li>• Clear cache regularly</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">Troubleshooting:</h4>
              <ul className="space-y-1 text-muted-foreground">
                <li>• Check your internet speed</li>
                <li>• Verify playlist URL is correct</li>
                <li>• Try different video decoders</li>
                <li>• Restart the app if streams freeze</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
}
