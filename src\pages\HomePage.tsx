
import { <PERSON> } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Navbar } from '@/components/navbar';
import { Footer } from '@/components/footer';
import { Tv, Download, BookOpen, Play, Shield, Users } from 'lucide-react';

export default function HomePage() {
  const featuredIPTV = [
    {
      title: "Premium Sports Package",
      type: "Xtream Codes",
      description: "Complete sports coverage with HD channels",
      expiry: "30 days",
      tags: ["Sports", "HD", "Live"]
    },
    {
      title: "Global Entertainment",
      type: "M3U Playlist",
      description: "International channels from around the world",
      expiry: "60 days",
      tags: ["International", "Movies", "TV Shows"]
    },
    {
      title: "Arabic IPTV Collection",
      type: "MAC Portal",
      description: "Arabic channels with premium content",
      expiry: "45 days",
      tags: ["Arabic", "Premium", "Entertainment"]
    }
  ];

  const features = [
    {
      icon: Tv,
      title: "Multiple IPTV Types",
      description: "Support for Xtream Codes, MAC Portals, M3U playlists, and direct file uploads"
    },
    {
      icon: Download,
      title: "Player Recommendations",
      description: "Curated list of the best IPTV players for all platforms"
    },
    {
      icon: Shield,
      title: "Secure & Reliable",
      description: "All content is verified and regularly updated for best performance"
    },
    {
      icon: Users,
      title: "Community Driven",
      description: "Content and recommendations from our active community"
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      <Navbar />
      
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-purple-900/20 via-blue-900/20 to-background">
        <div className="absolute inset-0 bg-grid-white/[0.02] bg-[size:60px_60px]" />
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Your Ultimate{' '}
              <span className="bg-gradient-to-r from-purple-500 to-blue-600 bg-clip-text text-transparent">
                IPTV Hub
              </span>
            </h1>
            <p className="text-xl text-muted-foreground mb-8 max-w-3xl mx-auto">
              Discover premium IPTV content, download the best players, and stay updated with our comprehensive guides and tutorials.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" className="bg-gradient-to-r from-purple-500 to-blue-600 hover:from-purple-600 hover:to-blue-700">
                <Link to="/iptv">
                  <Tv className="w-5 h-5 mr-2" />
                  Browse IPTV
                </Link>
              </Button>
              <Button asChild variant="outline" size="lg">
                <Link to="/apps">
                  <Download className="w-5 h-5 mr-2" />
                  Get Players
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Featured IPTV Section */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Featured IPTV of the Day</h2>
            <p className="text-muted-foreground">Handpicked premium content updated daily</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {featuredIPTV.map((item, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow border-border/50 hover:border-purple-500/50">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <Badge variant="outline" className="text-purple-500 border-purple-500/50">
                      {item.type}
                    </Badge>
                    <Badge variant="secondary">
                      {item.expiry}
                    </Badge>
                  </div>
                  <CardTitle className="text-xl">{item.title}</CardTitle>
                  <CardDescription>{item.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2 mb-4">
                    {item.tags.map((tag, tagIndex) => (
                      <Badge key={tagIndex} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                  <Button className="w-full" variant="outline">
                    <Play className="w-4 h-4 mr-2" />
                    View Details
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-muted/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Why Choose IPTV Hub?</h2>
            <p className="text-muted-foreground">Everything you need for the perfect IPTV experience</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {features.map((feature, index) => (
              <Card key={index} className="text-center border-border/50">
                <CardHeader>
                  <div className="w-12 h-12 mx-auto mb-4 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg flex items-center justify-center">
                    <feature.icon className="w-6 h-6 text-white" />
                  </div>
                  <CardTitle className="text-lg">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">{feature.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold mb-4">Ready to Get Started?</h2>
          <p className="text-muted-foreground mb-8">
            Join thousands of users who trust IPTV Hub for their streaming needs
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild size="lg" className="bg-gradient-to-r from-purple-500 to-blue-600 hover:from-purple-600 hover:to-blue-700">
              <Link to="/iptv">
                <Tv className="w-5 h-5 mr-2" />
                Browse IPTV Content
              </Link>
            </Button>
            <Button asChild variant="outline" size="lg">
              <Link to="/blog">
                <BookOpen className="w-5 h-5 mr-2" />
                Read Our Blog
              </Link>
            </Button>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
