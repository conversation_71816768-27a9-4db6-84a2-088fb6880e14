
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { AdminLayout } from '@/components/admin-layout';
import { Tv, Download, BookOpen, Users, Eye, TrendingUp } from 'lucide-react';

export default function AdminDashboard() {
  const stats = [
    {
      title: "Total IPTV Entries",
      value: "156",
      change: "+12 this month",
      icon: Tv,
      color: "text-purple-500"
    },
    {
      title: "Apps & Players",
      value: "24",
      change: "+3 this month",
      icon: Download,
      color: "text-blue-500"
    },
    {
      title: "Blog Posts",
      value: "48",
      change: "+8 this month",
      icon: BookOpen,
      color: "text-green-500"
    },
    {
      title: "Monthly Visitors",
      value: "12,483",
      change: "+24% from last month",
      icon: Users,
      color: "text-orange-500"
    }
  ];

  const recentActivity = [
    { action: "New IPTV entry added", item: "Premium Sports Package", time: "2 hours ago" },
    { action: "Blog post published", item: "IPTV Setup Guide 2024", time: "5 hours ago" },
    { action: "App updated", item: "IPTV Smarters Pro", time: "1 day ago" },
    { action: "IPTV entry updated", item: "Global Entertainment", time: "2 days ago" },
    { action: "New blog post", item: "Best IPTV Players", time: "3 days ago" }
  ];

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold">Dashboard</h1>
          <p className="text-muted-foreground">
            Welcome back! Here's what's happening with your IPTV Hub.
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {stats.map((stat, index) => (
            <Card key={index}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {stat.title}
                </CardTitle>
                <stat.icon className={`h-4 w-4 ${stat.color}`} />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stat.value}</div>
                <p className="text-xs text-muted-foreground">
                  {stat.change}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Recent Activity */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
              <CardDescription>
                Latest updates and changes to your content
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentActivity.map((activity, index) => (
                  <div key={index} className="flex items-center space-x-4">
                    <div className="w-2 h-2 bg-purple-500 rounded-full" />
                    <div className="flex-1 space-y-1">
                      <p className="text-sm font-medium">{activity.action}</p>
                      <p className="text-sm text-muted-foreground">{activity.item}</p>
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {activity.time}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
              <CardDescription>
                Common tasks and shortcuts
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <Card className="p-4 hover:bg-accent transition-colors cursor-pointer">
                  <div className="flex items-center space-x-2">
                    <Tv className="w-5 h-5 text-purple-500" />
                    <span className="font-medium">Add IPTV</span>
                  </div>
                </Card>
                <Card className="p-4 hover:bg-accent transition-colors cursor-pointer">
                  <div className="flex items-center space-x-2">
                    <Download className="w-5 h-5 text-blue-500" />
                    <span className="font-medium">Add App</span>
                  </div>
                </Card>
                <Card className="p-4 hover:bg-accent transition-colors cursor-pointer">
                  <div className="flex items-center space-x-2">
                    <BookOpen className="w-5 h-5 text-green-500" />
                    <span className="font-medium">New Post</span>
                  </div>
                </Card>
                <Card className="p-4 hover:bg-accent transition-colors cursor-pointer">
                  <div className="flex items-center space-x-2">
                    <Eye className="w-5 h-5 text-orange-500" />
                    <span className="font-medium">View Site</span>
                  </div>
                </Card>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Performance Overview */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <TrendingUp className="w-5 h-5" />
              <span>Performance Overview</span>
            </CardTitle>
            <CardDescription>
              Your site's performance metrics
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-500">98.5%</div>
                <p className="text-sm text-muted-foreground">Uptime</p>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-500">1.24s</div>
                <p className="text-sm text-muted-foreground">Avg Load Time</p>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-500">4.8/5</div>
                <p className="text-sm text-muted-foreground">User Rating</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
}
