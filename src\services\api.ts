// API Service Layer for IPTV Management
import { toast } from 'sonner';

// Types
export interface IPTVEntry {
  id: string;
  name: string;
  type: 'xtream' | 'mac' | 'm3u' | 'file';
  description: string;
  status: 'active' | 'inactive' | 'expired';
  createdAt: Date;
  updatedAt: Date;
  expiryDate?: Date;
  tags: string[];
  
  // Xtream Codes specific
  host?: string;
  port?: string;
  username?: string;
  password?: string;
  
  // MAC Portal specific
  portal?: string;
  mac?: string;
  
  // M3U specific
  url?: string;
  
  // File specific
  filename?: string;
  fileSize?: string;
  downloadUrl?: string;
}

export interface IPTVStats {
  total: number;
  active: number;
  expired: number;
  byType: {
    xtream: number;
    mac: number;
    m3u: number;
    file: number;
  };
}

export interface BulkImportResult {
  success: number;
  failed: number;
  errors: string[];
  imported: IPTVEntry[];
}

// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001/api';

class APIService {
  private token: string | null = null;

  constructor() {
    this.token = localStorage.getItem('auth_token');
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`;
    
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...(this.token && { Authorization: `Bearer ${this.token}` }),
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        if (response.status === 401) {
          // Token expired or invalid
          localStorage.removeItem('auth_token');
          localStorage.removeItem('auth_user');
          window.location.href = '/secure-admin/login';
          throw new Error('Authentication required');
        }
        
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('API Request failed:', error);
      throw error;
    }
  }

  // Authentication
  async login(username: string, password: string) {
    const response = await this.request<{ token: string; user: any }>('/auth/login', {
      method: 'POST',
      body: JSON.stringify({ username, password }),
    });
    
    this.token = response.token;
    return response;
  }

  async refreshToken() {
    const response = await this.request<{ token: string }>('/auth/refresh', {
      method: 'POST',
    });
    
    this.token = response.token;
    return response;
  }

  // IPTV Management
  async getIPTVEntries(params?: {
    page?: number;
    limit?: number;
    search?: string;
    type?: string;
    status?: string;
  }): Promise<{ entries: IPTVEntry[]; total: number; page: number; totalPages: number }> {
    const searchParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          searchParams.append(key, value.toString());
        }
      });
    }
    
    return this.request<any>(`/iptv?${searchParams.toString()}`);
  }

  async getIPTVEntry(id: string): Promise<IPTVEntry> {
    return this.request<IPTVEntry>(`/iptv/${id}`);
  }

  async createIPTVEntry(entry: Omit<IPTVEntry, 'id' | 'createdAt' | 'updatedAt'>): Promise<IPTVEntry> {
    return this.request<IPTVEntry>('/iptv', {
      method: 'POST',
      body: JSON.stringify(entry),
    });
  }

  async updateIPTVEntry(id: string, entry: Partial<IPTVEntry>): Promise<IPTVEntry> {
    return this.request<IPTVEntry>(`/iptv/${id}`, {
      method: 'PUT',
      body: JSON.stringify(entry),
    });
  }

  async deleteIPTVEntry(id: string): Promise<void> {
    return this.request<void>(`/iptv/${id}`, {
      method: 'DELETE',
    });
  }

  async bulkDeleteIPTVEntries(ids: string[]): Promise<void> {
    return this.request<void>('/iptv/bulk-delete', {
      method: 'POST',
      body: JSON.stringify({ ids }),
    });
  }

  // Auto-detection and bulk import
  async detectIPTVType(input: string): Promise<{
    type: 'xtream' | 'mac' | 'm3u' | 'unknown';
    confidence: number;
    extractedData?: Partial<IPTVEntry>;
  }> {
    return this.request<any>('/iptv/detect', {
      method: 'POST',
      body: JSON.stringify({ input }),
    });
  }

  async bulkImportFromText(text: string): Promise<BulkImportResult> {
    return this.request<BulkImportResult>('/iptv/bulk-import/text', {
      method: 'POST',
      body: JSON.stringify({ text }),
    });
  }

  async bulkImportFromFile(file: File): Promise<BulkImportResult> {
    const formData = new FormData();
    formData.append('file', file);
    
    return this.request<BulkImportResult>('/iptv/bulk-import/file', {
      method: 'POST',
      body: formData,
      headers: {}, // Remove Content-Type to let browser set it for FormData
    });
  }

  async bulkImportFromUrl(url: string): Promise<BulkImportResult> {
    return this.request<BulkImportResult>('/iptv/bulk-import/url', {
      method: 'POST',
      body: JSON.stringify({ url }),
    });
  }

  // Statistics
  async getIPTVStats(): Promise<IPTVStats> {
    return this.request<IPTVStats>('/iptv/stats');
  }

  // Export
  async exportIPTVEntries(format: 'json' | 'csv' | 'm3u'): Promise<Blob> {
    const response = await fetch(`${API_BASE_URL}/iptv/export?format=${format}`, {
      headers: {
        ...(this.token && { Authorization: `Bearer ${this.token}` }),
      },
    });
    
    if (!response.ok) {
      throw new Error('Export failed');
    }
    
    return response.blob();
  }
}

// Create singleton instance
export const apiService = new APIService();

// Mock data for development (remove when backend is ready)
export const mockIPTVEntries: IPTVEntry[] = [
  {
    id: '1',
    name: 'Premium Sports Package',
    type: 'xtream',
    description: 'Complete sports coverage with HD channels',
    status: 'active',
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-15'),
    expiryDate: new Date('2024-07-15'),
    tags: ['Sports', 'HD', 'Live'],
    host: 'iptv.example.com',
    port: '8080',
    username: 'sports_user',
    password: 'sports_pass',
  },
  {
    id: '2',
    name: 'Arabic IPTV Collection',
    type: 'mac',
    description: 'Arabic channels with premium content',
    status: 'active',
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-01-10'),
    expiryDate: new Date('2024-06-30'),
    tags: ['Arabic', 'Premium', 'Entertainment'],
    portal: 'http://portal.example.com/stalker_portal/c/',
    mac: '00:1A:79:XX:XX:XX',
  },
  {
    id: '3',
    name: 'USA Channels Playlist',
    type: 'm3u',
    description: 'US channels in high quality',
    status: 'active',
    createdAt: new Date('2024-01-05'),
    updatedAt: new Date('2024-01-05'),
    expiryDate: new Date('2024-09-10'),
    tags: ['USA', 'HD', 'Entertainment'],
    url: 'http://example.com/playlist.m3u',
  },
];
