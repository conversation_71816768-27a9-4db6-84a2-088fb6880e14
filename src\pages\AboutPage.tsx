
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Navbar } from '@/components/navbar';
import { Footer } from '@/components/footer';
import { Shield, Users, Award, Zap } from 'lucide-react';

export default function AboutPage() {
  const values = [
    {
      icon: Shield,
      title: "Trusted Content",
      description: "We ensure all IPTV content and recommendations are from legitimate sources and regularly verified."
    },
    {
      icon: Users,
      title: "Community First",
      description: "Our platform is built around our community's needs, with user feedback driving our content and features."
    },
    {
      icon: Award,
      title: "Quality Assurance",
      description: "Every IPTV service and app recommendation goes through thorough testing before being featured."
    },
    {
      icon: Zap,
      title: "Innovation",
      description: "We stay ahead of IPTV trends and technologies to provide you with the latest and best solutions."
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      <Navbar />
      
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold mb-4">
            About <span className="bg-gradient-to-r from-purple-500 to-blue-600 bg-clip-text text-transparent">IPTV Hub</span>
          </h1>
          <p className="text-xl text-muted-foreground">
            Your trusted source for IPTV content, applications, and comprehensive guides
          </p>
        </div>

        <div className="space-y-8">
          <Card>
            <CardHeader>
              <CardTitle>Our Mission</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground leading-relaxed">
                IPTV Hub was created to provide a centralized platform for IPTV enthusiasts to discover, learn, and access 
                high-quality streaming content and applications. We believe in making IPTV accessible to everyone while 
                maintaining the highest standards of quality and legality.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>What We Offer</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h4 className="font-semibold mb-2">Curated IPTV Content</h4>
                  <p className="text-muted-foreground">
                    We provide access to verified IPTV services including Xtream Codes, MAC Portals, M3U playlists, 
                    and downloadable files, all carefully vetted for quality and reliability.
                  </p>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">App Recommendations</h4>
                  <p className="text-muted-foreground">
                    Our team tests and recommends the best IPTV players and applications for all platforms, 
                    ensuring you have the optimal viewing experience.
                  </p>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">Educational Content</h4>
                  <p className="text-muted-foreground">
                    Through our comprehensive blog, we provide tutorials, guides, reviews, and news to help 
                    you stay informed about the latest in IPTV technology.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {values.map((value, index) => (
              <Card key={index}>
                <CardHeader>
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg flex items-center justify-center">
                      <value.icon className="w-5 h-5 text-white" />
                    </div>
                    <CardTitle className="text-lg">{value.title}</CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">{value.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Our Commitment</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-muted-foreground">
                  At IPTV Hub, we are committed to operating within legal boundaries and promoting legitimate 
                  IPTV services. We do not host or distribute copyrighted content and always encourage users 
                  to respect intellectual property rights.
                </p>
                <p className="text-muted-foreground">
                  Our platform serves as an educational resource and directory, helping users make informed 
                  decisions about IPTV services while staying compliant with applicable laws and regulations.
                </p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Contact Information</CardTitle>
              <CardDescription>
                Have questions or suggestions? We'd love to hear from you.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <p><strong>Email:</strong> <EMAIL></p>
                <p><strong>Support:</strong> <EMAIL></p>
                <p><strong>Business Inquiries:</strong> <EMAIL></p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <Footer />
    </div>
  );
}
