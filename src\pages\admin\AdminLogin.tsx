
import { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useAuth } from '@/contexts/auth-context';
import { Tv, Eye, EyeOff, Loader2, AlertCircle, Shield } from 'lucide-react';
import { toast } from 'sonner';

export default function AdminLogin() {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [loginAttempts, setLoginAttempts] = useState(0);
  const [isBlocked, setIsBlocked] = useState(false);
  const [blockTimeLeft, setBlockTimeLeft] = useState(0);
  const { login, isAuthenticated, isLoading } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  const from = location.state?.from?.pathname || '/admin/dashboard';
  const MAX_ATTEMPTS = 3;
  const BLOCK_DURATION = 300; // 5 minutes in seconds

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      navigate(from, { replace: true });
    }
  }, [isAuthenticated, navigate, from]);

  // Handle rate limiting
  useEffect(() => {
    const storedAttempts = localStorage.getItem('loginAttempts');
    const storedBlockTime = localStorage.getItem('blockUntil');

    if (storedAttempts) {
      setLoginAttempts(parseInt(storedAttempts));
    }

    if (storedBlockTime) {
      const blockUntil = parseInt(storedBlockTime);
      const now = Date.now();

      if (now < blockUntil) {
        setIsBlocked(true);
        setBlockTimeLeft(Math.ceil((blockUntil - now) / 1000));

        const interval = setInterval(() => {
          const timeLeft = Math.ceil((blockUntil - Date.now()) / 1000);
          if (timeLeft <= 0) {
            setIsBlocked(false);
            setBlockTimeLeft(0);
            setLoginAttempts(0);
            localStorage.removeItem('loginAttempts');
            localStorage.removeItem('blockUntil');
            clearInterval(interval);
          } else {
            setBlockTimeLeft(timeLeft);
          }
        }, 1000);

        return () => clearInterval(interval);
      } else {
        // Block time expired
        localStorage.removeItem('blockUntil');
        setIsBlocked(false);
      }
    }
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (isBlocked) {
      toast.error(`Too many failed attempts. Try again in ${Math.ceil(blockTimeLeft / 60)} minutes.`);
      return;
    }

    if (!username.trim() || !password.trim()) {
      toast.error('Please fill in all fields');
      return;
    }

    try {
      const success = await login(username, password);

      if (success) {
        // Reset attempts on successful login
        setLoginAttempts(0);
        localStorage.removeItem('loginAttempts');
        localStorage.removeItem('blockUntil');
        navigate(from, { replace: true });
      } else {
        // Increment failed attempts
        const newAttempts = loginAttempts + 1;
        setLoginAttempts(newAttempts);
        localStorage.setItem('loginAttempts', newAttempts.toString());

        if (newAttempts >= MAX_ATTEMPTS) {
          const blockUntil = Date.now() + (BLOCK_DURATION * 1000);
          localStorage.setItem('blockUntil', blockUntil.toString());
          setIsBlocked(true);
          setBlockTimeLeft(BLOCK_DURATION);
          toast.error(`Too many failed attempts. Account blocked for ${BLOCK_DURATION / 60} minutes.`);
        } else {
          toast.error(`Invalid credentials. ${MAX_ATTEMPTS - newAttempts} attempts remaining.`);
        }
      }
    } catch (error) {
      console.error('Login error:', error);
      toast.error('Login failed. Please try again.');
    }
  };

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-purple-50 to-blue-50 dark:from-purple-950/20 dark:to-blue-950/20 p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
            <Shield className="w-8 h-8 text-white" />
          </div>
          <CardTitle className="text-2xl">Secure Admin Access</CardTitle>
          <CardDescription>
            Sign in to access the IPTV management panel
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {isBlocked && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Account temporarily blocked due to multiple failed login attempts.
                <br />
                Time remaining: {formatTime(blockTimeLeft)}
              </AlertDescription>
            </Alert>
          )}

          {loginAttempts > 0 && !isBlocked && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                {loginAttempts} failed attempt{loginAttempts > 1 ? 's' : ''}.
                {MAX_ATTEMPTS - loginAttempts} remaining before account lock.
              </AlertDescription>
            </Alert>
          )}

          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="username">Username</Label>
              <Input
                id="username"
                type="text"
                placeholder="Enter your username"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                disabled={isBlocked || isLoading}
                required
                autoComplete="username"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="password">Password</Label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? 'text' : 'password'}
                  placeholder="Enter your password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  disabled={isBlocked || isLoading}
                  required
                  autoComplete="current-password"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={() => setShowPassword(!showPassword)}
                  disabled={isBlocked || isLoading}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>
            <Button
              type="submit"
              className="w-full bg-gradient-to-r from-purple-500 to-blue-600 hover:from-purple-600 hover:to-blue-700"
              disabled={isBlocked || isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Signing In...
                </>
              ) : (
                'Sign In'
              )}
            </Button>
          </form>

          <div className="mt-6 p-4 bg-muted rounded-lg">
            <h4 className="text-sm font-medium mb-2">Demo Credentials:</h4>
            <div className="text-xs text-muted-foreground space-y-1">
              <p><strong>Admin:</strong> admin / admin123</p>
              <p><strong>Moderator:</strong> moderator / mod123</p>
            </div>
          </div>

          <div className="text-center text-xs text-muted-foreground">
            <p>Protected by rate limiting and secure authentication</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
