// Export Service for IPTV Entries
import { IPTVEntry } from './api';

export class ExportService {
  static exportToJSON(entries: IPTVEntry[]): void {
    const dataStr = JSON.stringify(entries, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    this.downloadFile(dataBlob, 'iptv-entries.json');
  }

  static exportToCSV(entries: IPTVEntry[]): void {
    const headers = [
      'ID', 'Name', 'Type', 'Description', 'Status', 'Host', 'Port', 
      'Username', 'Password', 'Portal', 'MAC', 'URL', 'Tags', 'Expiry Date', 'Created At'
    ];
    
    const csvContent = [
      headers.join(','),
      ...entries.map(entry => [
        entry.id,
        `"${entry.name}"`,
        entry.type,
        `"${entry.description}"`,
        entry.status,
        entry.host || '',
        entry.port || '',
        entry.username || '',
        entry.password || '',
        entry.portal || '',
        entry.mac || '',
        entry.url || '',
        `"${entry.tags.join(';')}"`,
        entry.expiryDate ? new Date(entry.expiryDate).toISOString().split('T')[0] : '',
        new Date(entry.createdAt).toISOString().split('T')[0]
      ].join(','))
    ].join('\n');

    const dataBlob = new Blob([csvContent], { type: 'text/csv' });
    this.downloadFile(dataBlob, 'iptv-entries.csv');
  }

  static exportToM3U(entries: IPTVEntry[]): void {
    const m3uContent = [
      '#EXTM3U',
      ...entries
        .filter(entry => entry.type === 'm3u' && entry.url)
        .map(entry => [
          `#EXTINF:-1,${entry.name}`,
          entry.url
        ].join('\n'))
    ].join('\n');

    const dataBlob = new Blob([m3uContent], { type: 'audio/x-mpegurl' });
    this.downloadFile(dataBlob, 'iptv-playlist.m3u');
  }

  static exportXtreamList(entries: IPTVEntry[]): void {
    const xtreamEntries = entries.filter(entry => entry.type === 'xtream');
    const content = xtreamEntries.map(entry => 
      `Host: ${entry.host}\nPort: ${entry.port}\nUsername: ${entry.username}\nPassword: ${entry.password}\nName: ${entry.name}\n---`
    ).join('\n');

    const dataBlob = new Blob([content], { type: 'text/plain' });
    this.downloadFile(dataBlob, 'xtream-codes-list.txt');
  }

  static exportMACList(entries: IPTVEntry[]): void {
    const macEntries = entries.filter(entry => entry.type === 'mac');
    const content = macEntries.map(entry => 
      `Portal: ${entry.portal}\nMAC: ${entry.mac}\nName: ${entry.name}\n---`
    ).join('\n');

    const dataBlob = new Blob([content], { type: 'text/plain' });
    this.downloadFile(dataBlob, 'mac-portals-list.txt');
  }

  private static downloadFile(blob: Blob, filename: string): void {
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }

  static getExportStats(entries: IPTVEntry[]) {
    return {
      total: entries.length,
      xtream: entries.filter(e => e.type === 'xtream').length,
      mac: entries.filter(e => e.type === 'mac').length,
      m3u: entries.filter(e => e.type === 'm3u').length,
      file: entries.filter(e => e.type === 'file').length,
    };
  }
}
