
import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { AdminLayout } from '@/components/admin-layout';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Plus, Edit, Trash2, Copy, Eye } from 'lucide-react';
import { toast } from 'sonner';

export default function AdminIPTV() {
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [editingItem, setEditingItem] = useState(null);

  const iptvEntries = [
    {
      id: 1,
      name: "Premium Sports Package",
      type: "xtream",
      description: "Complete sports coverage with HD channels",
      host: "iptv.example.com",
      port: "8080",
      username: "sports_user",
      password: "sports_pass",
      expiry: "2024-07-15",
      tags: ["Sports", "HD", "Live"],
      status: "active"
    },
    {
      id: 2,
      name: "Arabic IPTV Collection",
      type: "mac",
      description: "Arabic channels with premium content",
      portal: "http://portal.example.com/stalker_portal/c/",
      mac: "00:1A:79:XX:XX:XX",
      expiry: "2024-06-30",
      tags: ["Arabic", "Premium", "Entertainment"],
      status: "active"
    },
    {
      id: 3,
      name: "USA Channels Playlist",
      type: "m3u",
      description: "US channels in high quality",
      url: "http://example.com/playlist.m3u",
      expiry: "2024-09-10",
      tags: ["USA", "HD", "Entertainment"],
      status: "active"
    }
  ];

  const handleAddIPTV = () => {
    toast.success('IPTV entry added successfully!');
    setShowAddDialog(false);
  };

  const handleEditIPTV = (item) => {
    setEditingItem(item);
    setShowAddDialog(true);
  };

  const handleDeleteIPTV = (id) => {
    toast.success('IPTV entry deleted successfully!');
  };

  const IPTVForm = () => (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="name">IPTV Name</Label>
          <Input id="name" placeholder="Enter IPTV name" />
        </div>
        <div className="space-y-2">
          <Label htmlFor="type">Type</Label>
          <Select>
            <SelectTrigger>
              <SelectValue placeholder="Select type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="xtream">Xtream Codes</SelectItem>
              <SelectItem value="mac">MAC Portal</SelectItem>
              <SelectItem value="m3u">M3U Playlist</SelectItem>
              <SelectItem value="file">File Upload</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="description">Description</Label>
        <Textarea id="description" placeholder="Enter description" />
      </div>
      
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="host">Host/URL</Label>
          <Input id="host" placeholder="Enter host or URL" />
        </div>
        <div className="space-y-2">
          <Label htmlFor="port">Port</Label>
          <Input id="port" placeholder="Enter port" />
        </div>
      </div>
      
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="username">Username</Label>
          <Input id="username" placeholder="Enter username" />
        </div>
        <div className="space-y-2">
          <Label htmlFor="password">Password</Label>
          <Input id="password" placeholder="Enter password" />
        </div>
      </div>
      
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="expiry">Expiry Date</Label>
          <Input id="expiry" type="date" />
        </div>
        <div className="space-y-2">
          <Label htmlFor="tags">Tags (comma separated)</Label>
          <Input id="tags" placeholder="Sports, HD, Live" />
        </div>
      </div>
      
      <div className="flex justify-end space-x-2">
        <Button variant="outline" onClick={() => setShowAddDialog(false)}>
          Cancel
        </Button>
        <Button onClick={handleAddIPTV}>
          {editingItem ? 'Update' : 'Add'} IPTV
        </Button>
      </div>
    </div>
  );

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">IPTV Management</h1>
            <p className="text-muted-foreground">
              Manage your IPTV content and entries
            </p>
          </div>
          <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
            <DialogTrigger asChild>
              <Button className="bg-gradient-to-r from-purple-500 to-blue-600 hover:from-purple-600 hover:to-blue-700">
                <Plus className="w-4 h-4 mr-2" />
                Add IPTV Entry
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>{editingItem ? 'Edit' : 'Add New'} IPTV Entry</DialogTitle>
                <DialogDescription>
                  {editingItem ? 'Update the IPTV entry details' : 'Fill in the details to add a new IPTV entry'}
                </DialogDescription>
              </DialogHeader>
              <IPTVForm />
            </DialogContent>
          </Dialog>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Total Entries</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">156</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Xtream Codes</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">89</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">MAC Portals</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">34</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">M3U Playlists</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">33</div>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filter */}
        <Card>
          <CardHeader>
            <CardTitle>Search & Filter</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex gap-4">
              <Input placeholder="Search IPTV entries..." className="flex-1" />
              <Select>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Filter by type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="xtream">Xtream Codes</SelectItem>
                  <SelectItem value="mac">MAC Portal</SelectItem>
                  <SelectItem value="m3u">M3U Playlist</SelectItem>
                  <SelectItem value="file">File Upload</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* IPTV Entries */}
        <div className="grid grid-cols-1 gap-4">
          {iptvEntries.map((entry) => (
            <Card key={entry.id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Badge variant="outline" className={
                      entry.type === 'xtream' ? 'text-purple-500 border-purple-500/50' :
                      entry.type === 'mac' ? 'text-blue-500 border-blue-500/50' :
                      entry.type === 'm3u' ? 'text-green-500 border-green-500/50' :
                      'text-orange-500 border-orange-500/50'
                    }>
                      {entry.type.toUpperCase()}
                    </Badge>
                    <CardTitle className="text-lg">{entry.name}</CardTitle>
                    <Badge variant={entry.status === 'active' ? 'default' : 'secondary'}>
                      {entry.status}
                    </Badge>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button size="sm" variant="outline">
                      <Eye className="w-4 h-4 mr-2" />
                      Preview
                    </Button>
                    <Button size="sm" variant="outline" onClick={() => handleEditIPTV(entry)}>
                      <Edit className="w-4 h-4" />
                    </Button>
                    <Button size="sm" variant="outline" onClick={() => handleDeleteIPTV(entry.id)}>
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
                <CardDescription>{entry.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {entry.type === 'xtream' && (
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div className="flex items-center justify-between">
                        <span className="font-medium">Host:</span>
                        <div className="flex items-center space-x-2">
                          <span className="text-muted-foreground">{entry.host}</span>
                          <Button size="sm" variant="ghost" onClick={() => navigator.clipboard.writeText(entry.host)}>
                            <Copy className="w-3 h-3" />
                          </Button>
                        </div>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="font-medium">Port:</span>
                        <span className="text-muted-foreground">{entry.port}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="font-medium">Username:</span>
                        <span className="text-muted-foreground">{entry.username}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="font-medium">Password:</span>
                        <span className="text-muted-foreground">{entry.password}</span>
                      </div>
                    </div>
                  )}
                  
                  {entry.type === 'mac' && (
                    <div className="space-y-2 text-sm">
                      <div className="flex items-center justify-between">
                        <span className="font-medium">Portal:</span>
                        <span className="text-muted-foreground break-all">{entry.portal}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="font-medium">MAC:</span>
                        <span className="text-muted-foreground">{entry.mac}</span>
                      </div>
                    </div>
                  )}
                  
                  {entry.type === 'm3u' && (
                    <div className="text-sm">
                      <span className="font-medium">URL:</span>
                      <span className="text-muted-foreground ml-2 break-all">{entry.url}</span>
                    </div>
                  )}
                  
                  <div className="flex items-center justify-between">
                    <div className="flex flex-wrap gap-1">
                      {entry.tags.map((tag, index) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                    {entry.expiry && (
                      <Badge variant="outline" className="text-xs">
                        Expires: {new Date(entry.expiry).toLocaleDateString()}
                      </Badge>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </AdminLayout>
  );
}
