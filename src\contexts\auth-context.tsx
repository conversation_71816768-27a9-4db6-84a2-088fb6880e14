
import { createContext, useContext, useState, ReactNode, useEffect } from 'react';
import { toast } from 'sonner';

interface User {
  id: string;
  username: string;
  email: string;
  role: 'admin' | 'moderator';
  lastLogin?: Date;
}

interface AuthContextType {
  isAuthenticated: boolean;
  user: User | null;
  token: string | null;
  login: (username: string, password: string) => Promise<boolean>;
  logout: () => void;
  isLoading: boolean;
  refreshToken: () => Promise<boolean>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Simulated JWT token generation (replace with real backend)
const generateToken = (user: User): string => {
  const header = btoa(JSON.stringify({ alg: 'HS256', typ: 'JWT' }));
  const payload = btoa(JSON.stringify({
    sub: user.id,
    username: user.username,
    role: user.role,
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60) // 24 hours
  }));
  const signature = btoa('simulated-signature'); // In production, use proper HMAC
  return `${header}.${payload}.${signature}`;
};

// Simulated password hashing (replace with bcrypt in backend)
const hashPassword = (password: string): string => {
  // This is just for demo - use proper bcrypt in production
  return btoa(password + 'salt');
};

// Simulated user database (replace with real database)
const users = [
  {
    id: '1',
    username: 'admin',
    email: '<EMAIL>',
    role: 'admin' as const,
    passwordHash: hashPassword('admin123')
  },
  {
    id: '2',
    username: 'moderator',
    email: '<EMAIL>',
    role: 'moderator' as const,
    passwordHash: hashPassword('mod123')
  }
];

export function AuthProvider({ children }: { children: ReactNode }) {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Check for existing session on mount
  useEffect(() => {
    const checkExistingSession = () => {
      const storedToken = localStorage.getItem('auth_token');
      const storedUser = localStorage.getItem('auth_user');

      if (storedToken && storedUser) {
        try {
          const userData = JSON.parse(storedUser);
          // Verify token is not expired (simplified check)
          const payload = JSON.parse(atob(storedToken.split('.')[1]));
          if (payload.exp > Math.floor(Date.now() / 1000)) {
            setToken(storedToken);
            setUser(userData);
            setIsAuthenticated(true);
          } else {
            // Token expired, clear storage
            localStorage.removeItem('auth_token');
            localStorage.removeItem('auth_user');
          }
        } catch (error) {
          console.error('Error parsing stored auth data:', error);
          localStorage.removeItem('auth_token');
          localStorage.removeItem('auth_user');
        }
      }
      setIsLoading(false);
    };

    checkExistingSession();
  }, []);

  const login = async (username: string, password: string): Promise<boolean> => {
    console.log('Login function called with:', { username, password });
    setIsLoading(true);

    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));

      // Simple authentication check for debugging
      if (username === 'admin' && password === 'admin123') {
        const userData: User = {
          id: '1',
          username: 'admin',
          email: '<EMAIL>',
          role: 'admin',
          lastLogin: new Date()
        };

        const authToken = generateToken(userData);

        // Store in state and localStorage
        setUser(userData);
        setToken(authToken);
        setIsAuthenticated(true);
        localStorage.setItem('auth_token', authToken);
        localStorage.setItem('auth_user', JSON.stringify(userData));

        console.log('Login successful, user data:', userData);
        toast.success(`Welcome back, ${userData.username}!`);
        setIsLoading(false);
        return true;
      } else if (username === 'moderator' && password === 'mod123') {
        const userData: User = {
          id: '2',
          username: 'moderator',
          email: '<EMAIL>',
          role: 'moderator',
          lastLogin: new Date()
        };

        const authToken = generateToken(userData);

        // Store in state and localStorage
        setUser(userData);
        setToken(authToken);
        setIsAuthenticated(true);
        localStorage.setItem('auth_token', authToken);
        localStorage.setItem('auth_user', JSON.stringify(userData));

        console.log('Login successful, user data:', userData);
        toast.success(`Welcome back, ${userData.username}!`);
        setIsLoading(false);
        return true;
      } else {
        console.log('Invalid credentials provided');
        toast.error('Invalid username or password');
        setIsLoading(false);
        return false;
      }
    } catch (error) {
      console.error('Login error:', error);
      toast.error('Login failed. Please try again.');
      setIsLoading(false);
      return false;
    }
  };

  const logout = () => {
    setIsAuthenticated(false);
    setUser(null);
    setToken(null);
    localStorage.removeItem('auth_token');
    localStorage.removeItem('auth_user');
    toast.success('Logged out successfully');
  };

  const refreshToken = async (): Promise<boolean> => {
    if (!user) return false;

    try {
      const newToken = generateToken(user);
      setToken(newToken);
      localStorage.setItem('auth_token', newToken);
      return true;
    } catch (error) {
      console.error('Token refresh failed:', error);
      logout();
      return false;
    }
  };

  return (
    <AuthContext.Provider value={{
      isAuthenticated,
      user,
      token,
      login,
      logout,
      isLoading,
      refreshToken
    }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
