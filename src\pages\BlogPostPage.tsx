
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Navbar } from '@/components/navbar';
import { Footer } from '@/components/footer';
import { Calendar, User, Clock, ArrowLeft, Share2 } from 'lucide-react';

export default function BlogPostPage() {
  const { slug } = useParams();

  // Mock blog post data - in a real app, this would be fetched based on the slug
  const blogPost = {
    title: "Complete Guide to IPTV Setup in 2024",
    slug: "complete-guide-iptv-setup-2024",
    content: `
      <h2>Introduction</h2>
      <p>IPTV (Internet Protocol Television) has revolutionized how we consume television content. This comprehensive guide will walk you through everything you need to know about setting up IPTV in 2024.</p>
      
      <h2>What is IPTV?</h2>
      <p>IPTV stands for Internet Protocol Television, which is a system where television services are delivered using the Internet protocol suite over a packet-switched network such as a LAN or the Internet, instead of being delivered through traditional terrestrial, satellite signal, and cable television formats.</p>
      
      <h2>Types of IPTV Services</h2>
      <h3>1. Live Television</h3>
      <p>Live IPTV allows you to watch television shows as they are broadcast in real-time. This is similar to traditional TV broadcasting but delivered over the internet.</p>
      
      <h3>2. Video on Demand (VOD)</h3>
      <p>VOD services allow you to select and watch video content when you choose to, rather than at a scheduled broadcast time.</p>
      
      <h3>3. Time-shifted Media</h3>
      <p>This allows you to watch previously broadcast content at your convenience. Common examples include catch-up TV and start-over TV.</p>
      
      <h2>Setting Up IPTV</h2>
      <h3>Requirements</h3>
      <ul>
        <li>Stable internet connection (minimum 25 Mbps recommended)</li>
        <li>Compatible device (Smart TV, Android box, smartphone, etc.)</li>
        <li>IPTV app or player</li>
        <li>IPTV subscription or playlist</li>
      </ul>
      
      <h3>Step-by-Step Setup</h3>
      <ol>
        <li><strong>Choose Your Device:</strong> Select a compatible device such as Android TV box, Fire TV Stick, or smartphone.</li>
        <li><strong>Install IPTV Player:</strong> Download and install a reliable IPTV player like IPTV Smarters, TiviMate, or VLC.</li>
        <li><strong>Get IPTV Subscription:</strong> Obtain a legitimate IPTV subscription or playlist from a trusted provider.</li>
        <li><strong>Configure the App:</strong> Enter your subscription details or playlist URL in the IPTV player.</li>
        <li><strong>Test and Optimize:</strong> Test the setup and optimize settings for best performance.</li>
      </ol>
      
      <h2>Best IPTV Players</h2>
      <p>Here are some of the most popular IPTV players available:</p>
      <ul>
        <li><strong>IPTV Smarters Pro:</strong> User-friendly interface with advanced features</li>
        <li><strong>TiviMate:</strong> Excellent for Android TV with premium features</li>
        <li><strong>VLC Media Player:</strong> Free and reliable for basic IPTV streaming</li>
        <li><strong>Perfect Player:</strong> Lightweight with customizable interface</li>
      </ul>
      
      <h2>Troubleshooting Common Issues</h2>
      <h3>Buffering Problems</h3>
      <p>If you experience buffering, try:</p>
      <ul>
        <li>Checking your internet speed</li>
        <li>Reducing video quality</li>
        <li>Using a wired connection instead of Wi-Fi</li>
        <li>Clearing app cache</li>
      </ul>
      
      <h3>Connection Issues</h3>
      <p>For connection problems:</p>
      <ul>
        <li>Verify your playlist URL or subscription details</li>
        <li>Check if your ISP blocks IPTV traffic</li>
        <li>Try using a VPN</li>
        <li>Contact your IPTV provider</li>
      </ul>
      
      <h2>Legal Considerations</h2>
      <p>It's important to use IPTV services legally. Always ensure that:</p>
      <ul>
        <li>You have proper licensing for the content you're watching</li>
        <li>You use legitimate IPTV providers</li>
        <li>You comply with local copyright laws</li>
        <li>You avoid pirated content</li>
      </ul>
      
      <h2>Conclusion</h2>
      <p>IPTV offers a flexible and convenient way to watch television content. By following this guide, you should be able to set up and enjoy IPTV services on your preferred device. Remember to always use legitimate services and respect copyright laws.</p>
    `,
    category: "Tutorial",
    author: "IPTV Hub Team",
    publishDate: "2024-01-15",
    readTime: "8 min read",
    tags: ["IPTV", "Setup", "Tutorial", "Guide"]
  };

  return (
    <div className="min-h-screen bg-background">
      <Navbar />
      
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <Button variant="ghost" asChild className="mb-4">
            <Link to="/blog" className="flex items-center">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Blog
            </Link>
          </Button>
          
          <div className="flex items-center space-x-2 mb-4">
            <Badge variant="outline" className="text-purple-500 border-purple-500/50">
              {blogPost.category}
            </Badge>
            <div className="flex items-center text-sm text-muted-foreground">
              <Calendar className="w-4 h-4 mr-1" />
              {new Date(blogPost.publishDate).toLocaleDateString()}
            </div>
            <div className="flex items-center text-sm text-muted-foreground">
              <Clock className="w-4 h-4 mr-1" />
              {blogPost.readTime}
            </div>
          </div>
          
          <h1 className="text-4xl font-bold mb-4">{blogPost.title}</h1>
          
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-2 text-muted-foreground">
              <User className="w-4 h-4" />
              <span>By {blogPost.author}</span>
            </div>
            <Button variant="outline" size="sm">
              <Share2 className="w-4 h-4 mr-2" />
              Share
            </Button>
          </div>
          
          <div className="flex flex-wrap gap-2 mb-8">
            {blogPost.tags.map((tag, index) => (
              <Badge key={index} variant="secondary">
                {tag}
              </Badge>
            ))}
          </div>
        </div>

        <Card>
          <CardContent className="p-8">
            <div 
              className="prose prose-gray dark:prose-invert max-w-none"
              dangerouslySetInnerHTML={{ __html: blogPost.content }}
            />
          </CardContent>
        </Card>
        
        {/* Related Articles */}
        <div className="mt-12">
          <h3 className="text-2xl font-bold mb-6">Related Articles</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <Badge variant="outline" className="mb-2">Review</Badge>
                <h4 className="font-bold mb-2">Best IPTV Players Comparison 2024</h4>
                <p className="text-muted-foreground text-sm mb-4">Detailed comparison of the top IPTV players including features, pricing, and performance analysis.</p>
                <Link to="/blog/best-iptv-players-comparison-2024" className="text-purple-500 hover:text-purple-600 text-sm">
                  Read More →
                </Link>
              </CardContent>
            </Card>
            
            <Card className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <Badge variant="outline" className="mb-2">Support</Badge>
                <h4 className="font-bold mb-2">Troubleshooting Common IPTV Issues</h4>
                <p className="text-muted-foreground text-sm mb-4">Solutions to the most common IPTV problems including buffering, connection issues, and playback errors.</p>
                <Link to="/blog/troubleshooting-common-iptv-issues" className="text-purple-500 hover:text-purple-600 text-sm">
                  Read More →
                </Link>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
}
